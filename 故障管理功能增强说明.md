# 故障管理系统功能增强说明

## 概述
本次更新为故障管理系统添加了两项重要功能增强：
1. **故障记录删除功能**：管理员可以删除故障记录
2. **处理状态切换权限优化**：管理员可以操作任何故障记录的状态

## 功能详情

### 1. 故障记录删除功能

#### 功能描述
- 在故障列表表格的操作列中添加删除按钮
- 删除按钮仅对管理员用户（`allevel === 1`）可见
- 点击删除按钮时弹出确认对话框
- 确认后调用后端API删除数据库中对应的故障记录
- 删除成功后刷新表格数据并显示成功提示

#### 实现细节

**前端实现 (js/fault.js):**
```javascript
// 在generateFaultRow方法中添加删除按钮
const isAdmin = this.userInfo?.allevel === 1;
const deleteButton = isAdmin ? 
    `<button class="btn-delete" data-fault-id="${fault.id}" style="background-color: #dc3545; color: white; margin-left: 5px;">删除</button>` : 
    '';

// 绑定删除按钮事件
const deleteButtons = document.querySelectorAll('#searchTab .btn-delete');
deleteButtons.forEach(button => {
    button.addEventListener('click', () => {
        const faultId = button.getAttribute('data-fault-id');
        this.deleteFaultRecord(faultId);
    });
});

// 删除故障记录方法
async deleteFaultRecord(faultId) {
    if (!confirm('是否确认删除该条故障记录？')) {
        return;
    }
    // 调用删除API...
}
```

**后端实现 (php/delete_fault.php):**
```php
// 删除故障记录及相关附件
// 使用事务确保数据一致性
$conn->begin_transaction();

// 删除附件记录
$deleteFilesSql = "DELETE FROM fault_files WHERE fault_id = ?";

// 删除主记录
$deleteFaultSql = "DELETE FROM alarmlist WHERE id = ?";

$conn->commit();
```

#### 安全特性
- 前端权限检查：只有`allevel === 1`的用户才能看到删除按钮
- 二次确认：删除前弹出确认对话框防止误操作
- 事务处理：使用数据库事务确保删除操作的原子性
- 关联删除：同时删除故障记录和相关附件记录

### 2. 处理状态切换权限优化

#### 功能描述
- 修改权限判断逻辑，允许管理员操作任何故障记录
- 原权限：只有故障记录者本人可以操作
- 新权限：故障记录者本人 OR 管理员（`allevel === 1`）都可以操作

#### 实现细节

**权限判断逻辑更新:**
```javascript
// 原逻辑
const hasPermission = faultData.recorder === this.userInfo?.name;

// 新逻辑
const hasPermission = faultData.recorder === this.userInfo?.name || this.userInfo?.allevel === 1;
```

#### 权限说明
- **故障记录者**：可以编辑自己创建的故障记录
- **管理员用户**：可以编辑任何故障记录（`allevel === 1`）
- **其他用户**：只能查看故障记录，无法编辑

## 权限体系

### 用户权限字段
系统使用以下字段控制用户权限：
- `level`: 用户级别 (20=管理员, 21=OC, 22=LCM, 23=LOG)
- `splevel`: 备品管理权限级别
- `allevel`: 故障管理权限级别 (1=管理员, 0=普通用户)

### 权限矩阵
| 用户类型 | 查看故障 | 编辑自己的故障 | 编辑他人的故障 | 删除故障 |
|---------|---------|---------------|---------------|---------|
| 普通用户 | ✓ | ✓ | ✗ | ✗ |
| 故障记录者 | ✓ | ✓ | ✗ | ✗ |
| 管理员 (allevel=1) | ✓ | ✓ | ✓ | ✓ |

## 文件修改清单

### 新增文件
- `php/delete_fault.php` - 删除故障记录API
- `test_fault_features.html` - 功能测试页面
- `故障管理功能增强说明.md` - 本说明文档

### 修改文件
- `js/fault.js` - 添加删除功能和权限优化
  - `generateFaultRow()` - 添加删除按钮
  - `bindDetailButtons()` - 绑定删除按钮事件
  - `deleteFaultRecord()` - 新增删除方法
  - `handleStatusControl()` - 优化权限判断

## 测试说明

### 测试环境
1. 打开 `test_fault_features.html` 进行功能测试
2. 可以模拟管理员和普通用户进行权限测试

### 测试用例
1. **管理员用户测试**
   - 应该看到删除按钮
   - 可以删除任何故障记录
   - 可以编辑任何故障记录状态

2. **普通用户测试**
   - 不应该看到删除按钮
   - 只能编辑自己创建的故障记录
   - 无法删除故障记录

3. **删除功能测试**
   - 删除前有确认对话框
   - 删除成功后表格自动刷新
   - 删除失败时显示错误信息

## 注意事项

1. **数据安全**：删除操作不可逆，请谨慎使用
2. **权限控制**：确保只有授权用户才能执行删除操作
3. **数据完整性**：删除故障记录时会同时删除相关附件记录
4. **用户体验**：所有操作都有相应的用户反馈

## 后续优化建议

1. **软删除**：考虑实现软删除功能，标记删除而非物理删除
2. **操作日志**：记录删除操作的日志，便于审计
3. **批量操作**：支持批量删除功能
4. **权限细化**：可以进一步细化权限控制，如按科室、按时间等
