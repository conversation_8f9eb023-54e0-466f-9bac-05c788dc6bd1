<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>故障管理功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .user-info {
            background-color: #e8f4fd;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 15px;
        }
        .test-button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        .test-button:hover {
            background-color: #0056b3;
        }
        .test-button.danger {
            background-color: #dc3545;
        }
        .test-button.danger:hover {
            background-color: #c82333;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 5px;
            background-color: #f8f9fa;
            border-left: 4px solid #007bff;
        }
        .error {
            border-left-color: #dc3545;
            background-color: #f8d7da;
        }
        .success {
            border-left-color: #28a745;
            background-color: #d4edda;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>故障管理功能测试页面</h1>
        
        <div class="test-section">
            <h3>当前用户信息</h3>
            <div class="user-info" id="userInfo">
                <p>请先登录系统</p>
            </div>
            <button class="test-button" onclick="loadUserInfo()">加载用户信息</button>
            <button class="test-button" onclick="simulateAdminUser()">模拟管理员用户</button>
            <button class="test-button" onclick="simulateNormalUser()">模拟普通用户</button>
        </div>

        <div class="test-section">
            <h3>权限测试</h3>
            <p>测试删除按钮显示权限和状态切换权限</p>
            <button class="test-button" onclick="testDeleteButtonPermission()">测试删除按钮权限</button>
            <button class="test-button" onclick="testStatusChangePermission()">测试状态切换权限</button>
            <div id="permissionResult" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>删除功能测试</h3>
            <p>测试故障记录删除功能（请谨慎使用）</p>
            <input type="number" id="testFaultId" placeholder="输入故障记录ID" style="padding: 8px; margin-right: 10px;">
            <button class="test-button danger" onclick="testDeleteFault()">测试删除故障记录</button>
            <div id="deleteResult" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>功能验证</h3>
            <p>验证实际的故障管理页面功能</p>
            <button class="test-button" onclick="openFaultPage()">打开故障管理页面</button>
            <button class="test-button" onclick="checkFaultTableButtons()">检查表格按钮显示</button>
            <div id="verificationResult" class="result" style="display: none;"></div>
        </div>
    </div>

    <script>
        let currentUser = null;

        function loadUserInfo() {
            const userInfo = localStorage.getItem('userInfo');
            if (userInfo) {
                currentUser = JSON.parse(userInfo);
                displayUserInfo(currentUser);
            } else {
                showResult('userInfo', '未找到用户信息，请先登录系统', 'error');
            }
        }

        function simulateAdminUser() {
            currentUser = {
                account: 'admin',
                name: '管理员',
                department: '设备部',
                section: 'EQP',
                level: 20,
                splevel: 1,
                allevel: 1
            };
            localStorage.setItem('userInfo', JSON.stringify(currentUser));
            displayUserInfo(currentUser);
        }

        function simulateNormalUser() {
            currentUser = {
                account: 'user001',
                name: '普通用户',
                department: '生产部',
                section: 'PROD',
                level: 21,
                splevel: 0,
                allevel: 0
            };
            localStorage.setItem('userInfo', JSON.stringify(currentUser));
            displayUserInfo(currentUser);
        }

        function displayUserInfo(user) {
            const userInfoDiv = document.getElementById('userInfo');
            userInfoDiv.innerHTML = `
                <p><strong>账号:</strong> ${user.account}</p>
                <p><strong>姓名:</strong> ${user.name}</p>
                <p><strong>部门:</strong> ${user.department}</p>
                <p><strong>科室:</strong> ${user.section}</p>
                <p><strong>级别:</strong> ${user.level}</p>
                <p><strong>备品权限:</strong> ${user.splevel}</p>
                <p><strong>管理员权限:</strong> ${user.allevel} ${user.allevel === 1 ? '(管理员)' : '(普通用户)'}</p>
            `;
        }

        function testDeleteButtonPermission() {
            if (!currentUser) {
                showResult('permissionResult', '请先加载用户信息', 'error');
                return;
            }

            const isAdmin = currentUser.allevel === 1;
            const message = isAdmin ? 
                '✓ 当前用户是管理员，应该显示删除按钮' : 
                '✗ 当前用户不是管理员，不应该显示删除按钮';
            
            showResult('permissionResult', message, isAdmin ? 'success' : 'error');
        }

        function testStatusChangePermission() {
            if (!currentUser) {
                showResult('permissionResult', '请先加载用户信息', 'error');
                return;
            }

            const faultRecorder = '测试记录者';
            const hasPermission = faultRecorder === currentUser.name || currentUser.allevel === 1;
            
            let message = `测试故障记录者: ${faultRecorder}\n`;
            message += `当前用户: ${currentUser.name}\n`;
            message += `权限检查结果: ${hasPermission ? '✓ 有权限' : '✗ 无权限'}\n`;
            message += `原因: ${currentUser.allevel === 1 ? '管理员权限' : 
                       (faultRecorder === currentUser.name ? '记录者本人' : '无权限')}`;
            
            showResult('permissionResult', message, hasPermission ? 'success' : 'error');
        }

        async function testDeleteFault() {
            const faultId = document.getElementById('testFaultId').value;
            if (!faultId) {
                showResult('deleteResult', '请输入故障记录ID', 'error');
                return;
            }

            if (!currentUser || currentUser.allevel !== 1) {
                showResult('deleteResult', '只有管理员才能删除故障记录', 'error');
                return;
            }

            if (!confirm('确认要删除故障记录 ' + faultId + ' 吗？')) {
                return;
            }

            try {
                const response = await fetch('php/delete_fault.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ id: parseInt(faultId) })
                });

                const result = await response.json();
                if (result.success) {
                    showResult('deleteResult', '删除成功: ' + result.message, 'success');
                } else {
                    showResult('deleteResult', '删除失败: ' + result.message, 'error');
                }
            } catch (error) {
                showResult('deleteResult', '删除请求失败: ' + error.message, 'error');
            }
        }

        function openFaultPage() {
            window.open('fault.html', '_blank');
        }

        function checkFaultTableButtons() {
            showResult('verificationResult', '请在故障管理页面中检查：\n1. 管理员用户应该看到红色的"删除"按钮\n2. 普通用户不应该看到删除按钮\n3. 管理员可以编辑任何故障记录\n4. 普通用户只能编辑自己创建的故障记录', 'success');
        }

        function showResult(elementId, message, type) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.className = `result ${type}`;
            element.innerHTML = message.replace(/\n/g, '<br>');
        }

        // 页面加载时自动加载用户信息
        window.onload = function() {
            loadUserInfo();
        };
    </script>
</body>
</html>
